import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FloatingButtonComponent } from "./floating-button/floating-button.component";
import { EntitiesListComponent } from "./entities-list/entities-list.component";
import { EntityDetailsComponent } from "./entity-details/entity-details.component";

@NgModule({
  declarations: [
    FloatingButtonComponent,
    EntitiesListComponent,
    EntityDetailsComponent,
  ],
  imports: [CommonModule],
  exports: [
    FloatingButtonComponent,
    EntitiesListComponent,
    EntityDetailsComponent,
  ],
})
export class DynamicEntityModule {}
