import { Component, EventEmitter, Output } from "@angular/core";

interface EntityCard {
  id: string;
  title: string;
  customFieldsCount: number;
  description?: string;
  lastModified?: Date;
}

@Component({
  selector: "lib-entities-list",
  templateUrl: "./entities-list.component.html",
  styleUrls: ["./entities-list.component.scss"],
})
export class EntitiesListComponent {
  @Output() entitySelected = new EventEmitter<EntityCard>();

  // Mock data for demonstration
  entities: EntityCard[] = [
    {
      id: "1",
      title: "KPI",
      customFieldsCount: 12,
    },
    {
      id: "2",
      title: "Operation",
      customFieldsCount: 8,
    },
    {
      id: "3",
      title: "Benchmark",
      customFieldsCount: 6,
    },
    {
      id: "4",
      title: "Plans",
      customFieldsCount: 15,
    },
    {
      id: "5",
      title: "Partners",
      customFieldsCount: 9,
    },
    {
      id: "6",
      title: "Tasks",
      customFieldsCount: 11,
    },
    {
      id: "7",
      title: "Sub-tasks",
      customFieldsCount: 7,
    },
  ];

  constructor() {}

  onEntityClick(entity: EntityCard): void {
    this.entitySelected.emit(entity);
  }

  trackByEntityId(_index: number, entity: EntityCard): string {
    return entity.id;
  }
}
