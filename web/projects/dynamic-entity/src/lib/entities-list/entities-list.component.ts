import { Component, EventEmitter, Output } from "@angular/core";

interface EntityCard {
  id: string;
  title: string;
  customFieldsCount: number;
  description?: string;
  lastModified?: Date;
}

@Component({
  selector: "lib-entities-list",
  templateUrl: "./entities-list.component.html",
  styleUrls: ["./entities-list.component.scss"],
})
export class EntitiesListComponent {
  @Output() entitySelected = new EventEmitter<EntityCard>();

  // Mock data for demonstration
  entities: EntityCard[] = [
    {
      id: "1",
      title: "User Management",
      customFieldsCount: 8,
      description: "Manage user accounts and permissions",
      lastModified: new Date("2024-01-15"),
    },
    {
      id: "2",
      title: "Product Catalog",
      customFieldsCount: 12,
      description: "Product information and inventory",
      lastModified: new Date("2024-01-20"),
    },
    {
      id: "3",
      title: "Order Processing",
      customFieldsCount: 15,
      description: "Handle customer orders and fulfillment",
      lastModified: new Date("2024-01-18"),
    },
    {
      id: "4",
      title: "Customer Support",
      customFieldsCount: 6,
      description: "Support tickets and customer communication",
      lastModified: new Date("2024-01-22"),
    },
    {
      id: "5",
      title: "Analytics Dashboard",
      customFieldsCount: 20,
      description: "Business intelligence and reporting",
      lastModified: new Date("2024-01-25"),
    },
    {
      id: "6",
      title: "Content Management",
      customFieldsCount: 10,
      description: "Website content and media management",
      lastModified: new Date("2024-01-19"),
    },
  ];

  constructor() {}

  onEntityClick(entity: EntityCard): void {
    this.entitySelected.emit(entity);
  }

  trackByEntityId(_index: number, entity: EntityCard): string {
    return entity.id;
  }

  getRelativeTime(date: Date): string {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return "Today";
    if (diffInDays === 1) return "Yesterday";
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return `${Math.floor(diffInDays / 30)} months ago`;
  }
}
