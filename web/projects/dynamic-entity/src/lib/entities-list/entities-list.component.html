<div class="entities-list-container">
  <div class="entities-list-header">
    <div class="header-content">
      <h3>Entities</h3>
      <p class="header-subtitle">{{ entities.length }} entities available</p>
    </div>
    <button class="add-entity-btn" type="button" aria-label="Add new entity">
      <i class="fas fa-plus"></i>
      <span>New Entity</span>
    </button>
  </div>

  <div class="entities-grid">
    <div *ngFor="let entity of entities; trackBy: trackByEntityId" class="entity-card" (click)="onEntityClick(entity)" [attr.aria-label]="'Open ' + entity.title + ' entity'" tabindex="0" (keydown.enter)="onEntityClick(entity)" (keydown.space)="onEntityClick(entity)">
      <div class="card-header">
        <div class="entity-icon">
          <i class="fas fa-database"></i>
        </div>
        <div class="card-actions">
          <button class="action-btn" type="button" aria-label="More options" (click)="$event.stopPropagation()">
            <i class="fas fa-ellipsis-v"></i>
          </button>
        </div>
      </div>

      <div class="card-content">
        <h4 class="entity-title">{{ entity.title }}</h4>
        <p class="entity-description" *ngIf="entity.description">{{ entity.description }}</p>

        <div class="entity-stats">
          <div class="stat-item">
            <i class="fas fa-list"></i>
            <span class="stat-value">{{ entity.customFieldsCount }}</span>
            <span class="stat-label">Custom Fields</span>
          </div>
        </div>
      </div>

      <div class="card-footer">
        <div class="last-modified">
          <i class="fas fa-clock"></i>
          <span>{{ getRelativeTime(entity.lastModified!) }}</span>
        </div>
        <div class="card-arrow">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    </div>
  </div>
</div>
