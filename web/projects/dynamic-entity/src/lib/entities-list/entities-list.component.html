<div class="entities-list-container">
  <div class="entities-grid">
    <div *ngFor="let entity of entities; trackBy: trackByEntityId" class="entity-card" (click)="onEntityClick(entity)" [attr.aria-label]="'Open ' + entity.title + ' entity'" tabindex="0" (keydown.enter)="onEntityClick(entity)" (keydown.space)="onEntityClick(entity)">
      <div class="entity-icon">
        <i class="fas fa-database"></i>
      </div>

      <div class="card-content">
        <h4 class="entity-title">{{ entity.title }}</h4>
        <div class="entity-stats">
          <span class="stat-value">{{ entity.customFieldsCount }}</span>
          <span class="stat-label">Fields</span>
        </div>
      </div>
    </div>
  </div>
</div>
