.entities-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.entities-list-header {
  padding: 0 0 20px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 24px;

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #212529;
  }
}

.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.entity-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
  }

  &:active {
    transform: translateY(-2px);
  }

  .entity-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .entity-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #212529;
    }

    .entity-stats {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .stat-value {
        font-weight: 600;
        color: #007bff;
        font-size: 20px;
      }

      .stat-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .entities-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }

  .entity-card {
    padding: 20px;

    .entity-icon {
      width: 56px;
      height: 56px;
      font-size: 20px;
    }
  }
}

@media (max-width: 480px) {
  .entities-grid {
    grid-template-columns: 1fr;
  }

  .entities-list-header h3 {
    font-size: 18px;
  }

  .entity-card {
    padding: 16px;

    .entity-icon {
      width: 48px;
      height: 48px;
      font-size: 18px;
    }

    .card-content .entity-title {
      font-size: 16px;
    }
  }
}
