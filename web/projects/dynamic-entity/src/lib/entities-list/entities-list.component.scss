.entities-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.entities-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 20px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 24px;

  .header-content {
    h3 {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: #212529;
    }

    .header-subtitle {
      margin: 0;
      font-size: 14px;
      color: #6c757d;
    }
  }

  .add-entity-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
    }

    i {
      font-size: 12px;
    }
  }
}

.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.entity-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
  }

  &:active {
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .entity-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
  }

  .card-actions {
    .action-btn {
      width: 32px;
      height: 32px;
      border: none;
      background: transparent;
      color: #6c757d;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
        color: #495057;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
      }
    }
  }
}

.card-content {
  margin-bottom: 16px;

  .entity-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    line-height: 1.3;
  }

  .entity-description {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .entity-stats {
    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      i {
        color: #007bff;
        font-size: 14px;
      }

      .stat-value {
        font-weight: 600;
        color: #212529;
        font-size: 16px;
      }

      .stat-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid #f1f3f4;

  .last-modified {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6c757d;

    i {
      font-size: 11px;
    }
  }

  .card-arrow {
    color: #007bff;
    font-size: 12px;
    transition: transform 0.2s ease;
  }
}

.entity-card:hover .card-arrow {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .entities-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .entity-card {
    padding: 16px;
  }

  .entities-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .add-entity-btn {
      align-self: stretch;
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .entities-list-header {
    .header-content h3 {
      font-size: 18px;
    }
  }

  .card-header .entity-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .card-content .entity-title {
    font-size: 16px;
  }
}
