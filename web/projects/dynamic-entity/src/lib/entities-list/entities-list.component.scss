.entities-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  flex: 1;
  overflow-y: auto;
  padding: 4px;
}

.entity-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 12px;
  min-height: 140px;
  justify-content: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    border-color: #007bff;

    .entity-icon {
      transform: scale(1.05);
      box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
  }

  &:active {
    transform: translateY(0);
  }

  .entity-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    transition: all 0.25s ease;
    flex-shrink: 0;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;

    .entity-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #212529;
      line-height: 1.2;
    }

    .entity-stats {
      display: flex;
      align-items: center;
      gap: 6px;
      background-color: #f8f9fa;
      padding: 4px 8px;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .stat-value {
        font-weight: 700;
        color: #007bff;
        font-size: 14px;
      }

      .stat-label {
        font-size: 11px;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-weight: 500;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .entities-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
  }

  .entity-card {
    padding: 14px;
    min-height: 130px;

    .entity-icon {
      width: 44px;
      height: 44px;
      font-size: 18px;
    }

    .card-content .entity-title {
      font-size: 15px;
    }
  }
}

@media (max-width: 480px) {
  .entities-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 10px;
  }

  .entity-card {
    padding: 12px;
    min-height: 120px;

    .entity-icon {
      width: 40px;
      height: 40px;
      font-size: 16px;
    }

    .card-content {
      gap: 6px;

      .entity-title {
        font-size: 14px;
      }

      .entity-stats {
        padding: 3px 6px;

        .stat-value {
          font-size: 13px;
        }

        .stat-label {
          font-size: 10px;
        }
      }
    }
  }
}
