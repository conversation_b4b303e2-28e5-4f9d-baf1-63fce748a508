/* Floating <PERSON><PERSON> Styles */
.floating-button {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  z-index: 1000;
  transition: all 0.3s ease;

  &:hover {
    background-color: #0056b3;
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
    transform: translateY(-50%) scale(1.05);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }

  &:focus {
    outline: none;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3),
      0 0 0 3px rgba(0, 123, 255, 0.2);
  }
}

/* Popup Content */
.popup-content {
  position: fixed;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Ensure the element can be transformed */
  will-change: transform;
  z-index: 1000;
}

/* Popup Header */
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  min-height: 70px;
}

.popup-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  line-height: 1.2;
}

/* Close Button */
.close-button {
  width: 44px;
  height: 44px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    background-color: #e9ecef;
    color: #495057;
    border-color: #adb5bd;
  }

  &:active {
    transform: scale(0.95);
    background-color: #dee2e6;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
  }
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background-color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .floating-button {
    left: 15px;
    width: 50px;
    height: 50px;
    font-size: 18px;
  }

  .popup-header {
    padding: 16px 20px;
    min-height: 60px;
  }

  .popup-title {
    font-size: 18px;
  }

  .close-button {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .content-area {
    padding: 20px;
  }
}
