import { Component } from "@angular/core";
import { trigger, style, transition, animate } from "@angular/animations";

@Component({
  selector: "lib-floating-button",
  templateUrl: "./floating-button.component.html",
  styleUrls: ["./floating-button.component.scss"],
  animations: [
    trigger("slideUp", [
      transition(":enter", [
        style({ transform: "translateY(100vh)", opacity: 0 }),
        animate(
          "500ms ease-in-out",
          style({ transform: "translateY(0)", opacity: 1 })
        ),
      ]),
      transition(":leave", [
        animate(
          "500ms ease-in-out",
          style({ transform: "translateY(100vh)", opacity: 0 })
        ),
      ]),
    ]),
  ],
})
export class FloatingButtonComponent {
  isPopupOpen = false;

  openPopup(): void {
    this.isPopupOpen = true;
  }

  closePopup(): void {
    this.isPopupOpen = false;
  }
}
