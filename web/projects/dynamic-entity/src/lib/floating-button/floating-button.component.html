<!-- Floating Button -->
<button class="floating-button" (click)="openPopup()" type="button" aria-label="Open popup">
  <i class="fas fa-plus"></i>
</button>

<!-- Full Screen Popup -->
<div *ngIf="isPopupOpen" class="popup-content" [@slideUp]>
  <!-- Header -->
  <div class="popup-header">
    <h2 class="popup-title">Dynamic Entity</h2>
    <button class="close-button" (click)="closePopup()" type="button" aria-label="Close popup">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Content Area -->
  <div class="content-area">
    <lib-entities-list *ngIf="currentView === 'list'" (entitySelected)="onEntitySelected($event)"> </lib-entities-list>
    <lib-entity-details *ngIf="currentView === 'details'" [selectedEntity]="selectedEntity" (backToList)="onBackToList()"> </lib-entity-details>
  </div>
</div>
