.entity-details-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.entity-details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 20px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 24px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-button {
      width: 40px;
      height: 40px;
      border: 1px solid #dee2e6;
      background-color: #f8f9fa;
      color: #6c757d;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #e9ecef;
        color: #495057;
        border-color: #adb5bd;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
      }
    }

    .header-info {
      h3 {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 600;
        color: #212529;
      }

      .entity-subtitle {
        margin: 0;
        font-size: 14px;
        color: #6c757d;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;

    .action-button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      border: 1px solid;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &.primary {
        background-color: #007bff;
        color: white;
        border-color: #007bff;

        &:hover {
          background-color: #0056b3;
          border-color: #0056b3;
        }
      }

      &.secondary {
        background-color: white;
        color: #6c757d;
        border-color: #dee2e6;

        &:hover {
          background-color: #f8f9fa;
          color: #495057;
          border-color: #adb5bd;
        }
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
      }

      i {
        font-size: 12px;
      }
    }
  }
}

.entity-details-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.overview-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;

  .card-header {
    display: flex;
    gap: 16px;

    .entity-icon {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #007bff, #0056b3);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      flex-shrink: 0;
    }

    .entity-info {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #212529;
      }

      .description {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: #6c757d;
        line-height: 1.5;
      }

      .meta-info {
        display: flex;
        gap: 24px;
        flex-wrap: wrap;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #6c757d;

          i {
            color: #007bff;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    h5 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #212529;
    }

    .add-field-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #0056b3;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
      }

      i {
        font-size: 12px;
      }
    }
  }

  .fields-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }

  .field-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    }

    .field-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .field-type {
        width: 32px;
        height: 32px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #007bff;
        font-size: 14px;
      }

      .field-menu {
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        color: #6c757d;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f8f9fa;
          color: #495057;
        }

        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }
      }
    }

    .field-content {
      h6 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #212529;
      }

      .field-type-label {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #6c757d;
      }

      .field-properties {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .property-tag {
          padding: 4px 8px;
          background-color: #e7f3ff;
          color: #0066cc;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  padding: 60px 20px;

  .empty-icon {
    width: 80px;
    height: 80px;
    background-color: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    color: #6c757d;
    font-size: 32px;
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #212529;
  }

  p {
    margin: 0;
    font-size: 16px;
    color: #6c757d;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .entity-details-header {
    .header-actions {
      flex-direction: column;
      gap: 8px;

      .action-button {
        justify-content: center;
      }
    }
  }

  .overview-card {
    padding: 20px;

    .card-header {
      flex-direction: column;
      text-align: center;

      .entity-icon {
        align-self: center;
      }

      .entity-info .meta-info {
        justify-content: center;
      }
    }
  }

  .section .fields-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .entity-details-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-actions {
      align-self: stretch;
    }
  }

  .overview-card {
    padding: 16px;

    .card-header .entity-info h4 {
      font-size: 20px;
    }
  }

  .section .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .add-field-btn {
      align-self: stretch;
      justify-content: center;
    }
  }
}
