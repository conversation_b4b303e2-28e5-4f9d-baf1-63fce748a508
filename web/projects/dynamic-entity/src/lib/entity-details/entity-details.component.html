<div class="entity-details-container">
  <div class="entity-details-header">
    <div class="header-left">
      <button class="back-button" (click)="onBackClick()" type="button" aria-label="Back to entities list">
        <i class="fas fa-arrow-left"></i>
      </button>
      <div class="header-info" *ngIf="selectedEntity">
        <h3>{{ selectedEntity.title }}</h3>
        <p class="entity-subtitle">Entity Details</p>
      </div>
    </div>
    <div class="header-actions">
      <button class="action-button secondary" type="button">
        <i class="fas fa-edit"></i>
        <span>Edit</span>
      </button>
      <button class="action-button primary" type="button">
        <i class="fas fa-plus"></i>
        <span>Add Field</span>
      </button>
    </div>
  </div>

  <div class="entity-details-content" *ngIf="selectedEntity">
    <!-- Entity Overview Card -->
    <div class="overview-card">
      <div class="card-header">
        <div class="entity-icon">
          <i class="fas fa-database"></i>
        </div>
        <div class="entity-info">
          <h4>{{ selectedEntity.title }}</h4>
          <p class="description" *ngIf="selectedEntity.description">{{ selectedEntity.description }}</p>
          <div class="meta-info">
            <span class="meta-item">
              <i class="fas fa-clock"></i>
              Last modified {{ getRelativeTime(selectedEntity.lastModified!) }}
            </span>
            <span class="meta-item">
              <i class="fas fa-list"></i>
              {{ selectedEntity.customFieldsCount }} custom fields
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Custom Fields Section -->
    <div class="section">
      <div class="section-header">
        <h5>Custom Fields</h5>
        <button class="add-field-btn" type="button">
          <i class="fas fa-plus"></i>
          Add Field
        </button>
      </div>
      <div class="fields-grid">
        <!-- Mock custom fields -->
        <div class="field-card" *ngFor="let i of [1, 2, 3, 4, 5, 6, 7, 8]">
          <div class="field-header">
            <div class="field-type">
              <i class="fas fa-font"></i>
            </div>
            <button class="field-menu" type="button">
              <i class="fas fa-ellipsis-v"></i>
            </button>
          </div>
          <div class="field-content">
            <h6>Field Name {{ i }}</h6>
            <p class="field-type-label">Text Field</p>
            <div class="field-properties">
              <span class="property-tag">Required</span>
              <span class="property-tag">Searchable</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!selectedEntity">
    <div class="empty-icon">
      <i class="fas fa-cube"></i>
    </div>
    <h4>No Entity Selected</h4>
    <p>Select an entity from the list to view its details</p>
  </div>
</div>
