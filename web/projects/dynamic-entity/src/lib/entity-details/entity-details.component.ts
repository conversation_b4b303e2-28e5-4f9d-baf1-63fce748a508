import { Component, EventEmitter, Input, Output } from "@angular/core";

interface EntityCard {
  id: string;
  title: string;
  customFieldsCount: number;
  description?: string;
  lastModified?: Date;
}

@Component({
  selector: "lib-entity-details",
  templateUrl: "./entity-details.component.html",
  styleUrls: ["./entity-details.component.scss"],
})
export class EntityDetailsComponent {
  @Input() selectedEntity: EntityCard | null = null;
  @Output() backToList = new EventEmitter<void>();

  constructor() {}

  onBackClick(): void {
    this.backToList.emit();
  }

  getRelativeTime(date: Date): string {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return "Today";
    if (diffInDays === 1) return "Yesterday";
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return `${Math.floor(diffInDays / 30)} months ago`;
  }
}
